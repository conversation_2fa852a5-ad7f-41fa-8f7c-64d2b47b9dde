#!/usr/bin/env python3
"""
测试获取当前用户详细信息API

使用方法:
1. 确保服务器正在运行
2. 先通过登录接口获取access_token
3. 使用token调用 /api/v1/users/me 接口
"""

import requests
import json

# 服务器配置
BASE_URL = "http://localhost:8001"
API_PREFIX = "/api/v1"

def test_current_user_info():
    """
    测试获取当前用户信息接口
    
    注意：需要先获取有效的access_token
    """
    print("=== 测试获取当前用户详细信息API ===")
    
    # 这里需要替换为实际的access_token
    # 可以通过以下方式获取：
    # 1. 通过SMS登录: POST /api/v1/sms-auth/login
    # 2. 通过微信登录: POST /api/v1/wechat/login
    access_token = "your_access_token_here"
    
    if access_token == "your_access_token_here":
        print("❌ 请先获取有效的access_token")
        print("\n获取token的方法:")
        print("1. SMS登录: POST /api/v1/sms-auth/login")
        print("2. 微信登录: POST /api/v1/wechat/login")
        return
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # 调用获取当前用户信息接口
    url = f"{BASE_URL}{API_PREFIX}/users/me"
    
    try:
        print(f"📡 请求URL: {url}")
        response = requests.get(url, headers=headers)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            user_info = response.json()
            print("✅ 成功获取用户信息:")
            print(json.dumps(user_info, indent=2, ensure_ascii=False))
            
            # 验证返回字段
            required_fields = ['id', 'username', 'is_active', 'created_at']
            missing_fields = [field for field in required_fields if field not in user_info]
            
            if missing_fields:
                print(f"⚠️  缺少必需字段: {missing_fields}")
            else:
                print("✅ 所有必需字段都存在")
                
        elif response.status_code == 401:
            print("❌ 认证失败，请检查access_token是否有效")
            print(f"响应内容: {response.text}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")

def show_api_documentation():
    """
    显示API文档信息
    """
    print("\n=== API文档 ===")
    print(f"📚 Swagger文档: {BASE_URL}/docs")
    print(f"📚 ReDoc文档: {BASE_URL}/redoc")
    print("\n=== 新增的API端点 ===")
    print(f"🔗 GET {API_PREFIX}/users/me")
    print("   描述: 获取当前用户的详细信息")
    print("   认证: 需要Bearer Token")
    print("   响应: UnifiedUserInfo格式")
    print("\n=== 响应字段说明 ===")
    print("   - id: 用户ID")
    print("   - username: 用户名")
    print("   - nickname: 昵称")
    print("   - email: 邮箱")
    print("   - avatar: 头像URL")
    print("   - is_active: 是否激活")
    print("   - last_login: 最后登录时间")
    print("   - created_at: 创建时间")
    print("   - wechat_nickname: 微信昵称")
    print("   - wechat_avatar: 微信头像")
    print("   - login_type: 登录方式")

if __name__ == "__main__":
    show_api_documentation()
    test_current_user_info()