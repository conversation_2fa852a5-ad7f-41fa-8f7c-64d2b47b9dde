"""
新的权限依赖函数

基于重新设计的权限系统，提供更灵活和一致的权限检查
"""

from typing import Callable, List, Optional
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user, get_current_user_optional
from app.core.permission_system import (
    Permission, PermissionChecker, ResourceType, Action, Scope
)
from app.db.session import get_db
from app.models.user import User


def require_permissions(permissions: List[Permission]) -> Callable:
    """
    要求用户拥有指定权限的依赖函数
    
    Args:
        permissions: 所需权限列表
        
    Returns:
        依赖函数
    """
    async def permission_dependency(
        current_user: User = Depends(get_current_user)
    ) -> User:
        for permission in permissions:
            PermissionChecker.require_permission(current_user, permission)
        return current_user
    
    return permission_dependency


def require_permission(permission: Permission) -> Callable:
    """
    要求用户拥有单个权限的依赖函数
    
    Args:
        permission: 所需权限
        
    Returns:
        依赖函数
    """
    return require_permissions([permission])


def require_resource_access(
    resource_type: ResourceType, 
    action: Action,
    resource_id_param: str = "id",
    allow_public: bool = False
) -> Callable:
    """
    要求用户拥有资源访问权限的依赖函数
    
    Args:
        resource_type: 资源类型
        action: 操作类型
        resource_id_param: 资源ID参数名
        allow_public: 是否允许公开访问
        
    Returns:
        依赖函数
    """
    async def resource_permission_dependency(
        current_user: User = Depends(get_current_user),
        db: AsyncSession = Depends(get_db),
        **kwargs
    ) -> User:
        # 获取资源ID
        resource_id = kwargs.get(resource_id_param)
        if not resource_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"缺少资源ID参数: {resource_id_param}"
            )
        
        # 获取资源信息
        resource = await get_resource_by_id(db, resource_type, resource_id)
        if not resource:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{resource_type} 不存在"
            )
        
        # 检查权限
        resource_owner_id = getattr(resource, 'author_id', None) or getattr(resource, 'user_id', None)
        is_public = getattr(resource, 'is_published', False) and getattr(resource, 'is_approved', False)
        
        PermissionChecker.require_resource_access(
            current_user, resource_type, action, resource_owner_id, 
            is_public and allow_public
        )
        
        return current_user
    
    return resource_permission_dependency


def optional_resource_access(
    resource_type: ResourceType,
    action: Action,
    resource_id_param: str = "id",
    allow_public: bool = True
) -> Callable:
    """
    可选的资源访问权限检查（支持游客访问）
    
    Args:
        resource_type: 资源类型
        action: 操作类型
        resource_id_param: 资源ID参数名
        allow_public: 是否允许公开访问
        
    Returns:
        依赖函数
    """
    async def optional_resource_permission_dependency(
        current_user: Optional[User] = Depends(get_current_user_optional),
        db: AsyncSession = Depends(get_db),
        **kwargs
    ) -> Optional[User]:
        # 获取资源ID
        resource_id = kwargs.get(resource_id_param)
        if not resource_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"缺少资源ID参数: {resource_id_param}"
            )
        
        # 获取资源信息
        resource = await get_resource_by_id(db, resource_type, resource_id)
        if not resource:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"{resource_type} 不存在"
            )
        
        # 检查权限
        resource_owner_id = getattr(resource, 'author_id', None) or getattr(resource, 'user_id', None)
        is_public = getattr(resource, 'is_published', False) and getattr(resource, 'is_approved', False)
        
        # 如果没有权限访问，抛出异常
        if not PermissionChecker.check_resource_access(
            current_user, resource_type, action, resource_owner_id,
            is_public and allow_public
        ):
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要登录才能访问此资源"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，无法对 {resource_type} 执行 {action} 操作"
                )
        
        return current_user
    
    return optional_resource_permission_dependency


async def get_resource_by_id(db: AsyncSession, resource_type: ResourceType, resource_id: int):
    """根据资源类型和ID获取资源"""
    from sqlalchemy import select
    
    if resource_type == ResourceType.USER:
        from app.models.user import User
        result = await db.execute(select(User).where(User.id == resource_id))
        return result.scalar_one_or_none()
    
    elif resource_type == ResourceType.ARTICLE:
        from app.models.article import Article
        result = await db.execute(select(Article).where(Article.id == resource_id))
        return result.scalar_one_or_none()
    
    elif resource_type == ResourceType.VIDEO:
        from app.models.video import Video
        result = await db.execute(select(Video).where(Video.id == resource_id))
        return result.scalar_one_or_none()
    
    elif resource_type == ResourceType.COMMENT:
        from app.models.comment import Comment
        result = await db.execute(select(Comment).where(Comment.id == resource_id))
        return result.scalar_one_or_none()
    
    return None


# 常用权限依赖
class PermissionDeps:
    """常用权限依赖"""
    
    # 用户相关
    user_read_all = require_permission(
        Permission(ResourceType.USER, Action.READ, Scope.ALL)
    )
    user_manage = require_permission(
        Permission(ResourceType.USER, Action.MANAGE, Scope.ALL)
    )
    
    # 文章相关
    article_create = require_permission(
        Permission(ResourceType.ARTICLE, Action.CREATE, Scope.OWN)
    )
    article_manage = require_permission(
        Permission(ResourceType.ARTICLE, Action.MANAGE, Scope.ALL)
    )
    article_approve = require_permission(
        Permission(ResourceType.ARTICLE, Action.APPROVE, Scope.ALL)
    )
    
    # 视频相关
    video_create = require_permission(
        Permission(ResourceType.VIDEO, Action.CREATE, Scope.OWN)
    )
    video_manage = require_permission(
        Permission(ResourceType.VIDEO, Action.MANAGE, Scope.ALL)
    )
    video_approve = require_permission(
        Permission(ResourceType.VIDEO, Action.APPROVE, Scope.ALL)
    )
    
    # 系统相关
    system_manage = require_permission(
        Permission(ResourceType.SYSTEM, Action.MANAGE, Scope.ALL)
    )


# 资源访问依赖
class ResourceAccessDeps:
    """资源访问依赖"""
    
    # 文章访问
    article_read = lambda article_id_param="article_id": optional_resource_access(
        ResourceType.ARTICLE, Action.READ, article_id_param, allow_public=True
    )
    
    article_update = lambda article_id_param="article_id": require_resource_access(
        ResourceType.ARTICLE, Action.UPDATE, article_id_param
    )
    
    article_delete = lambda article_id_param="article_id": require_resource_access(
        ResourceType.ARTICLE, Action.DELETE, article_id_param
    )
    
    # 视频访问
    video_read = lambda video_id_param="video_id": optional_resource_access(
        ResourceType.VIDEO, Action.READ, video_id_param, allow_public=True
    )
    
    video_update = lambda video_id_param="video_id": require_resource_access(
        ResourceType.VIDEO, Action.UPDATE, video_id_param
    )
    
    video_delete = lambda video_id_param="video_id": require_resource_access(
        ResourceType.VIDEO, Action.DELETE, video_id_param
    )
    
    # 用户访问
    user_read = lambda user_id_param="user_id": require_resource_access(
        ResourceType.USER, Action.READ, user_id_param
    )
    
    user_update = lambda user_id_param="user_id": require_resource_access(
        ResourceType.USER, Action.UPDATE, user_id_param
    )
    
    user_delete = lambda user_id_param="user_id": require_resource_access(
        ResourceType.USER, Action.DELETE, user_id_param
    )


def check_self_access(current_user: User, target_user_id: int) -> bool:
    """检查是否访问自己的资源"""
    return current_user.id == target_user_id


def require_self_or_permission(permission: Permission) -> Callable:
    """
    要求是自己的资源或拥有指定权限
    
    Args:
        permission: 所需权限
        
    Returns:
        依赖函数
    """
    async def self_or_permission_dependency(
        current_user: User = Depends(get_current_user),
        user_id: int = None,
        **kwargs
    ) -> User:
        # 如果是访问自己的资源，直接允许
        if user_id and check_self_access(current_user, user_id):
            return current_user
        
        # 否则检查权限
        PermissionChecker.require_permission(current_user, permission)
        return current_user
    
    return self_or_permission_dependency
