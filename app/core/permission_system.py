"""
重新设计的权限系统

基于 RBAC (Role-Based Access Control) 模型，支持：
1. 资源级权限控制
2. 所有权检查
3. 动态权限分配
4. 权限继承
"""

from enum import Enum
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass

from fastapi import HTTPException, status


class ResourceType(str, Enum):
    """资源类型枚举"""
    USER = "user"
    ARTICLE = "article"
    VIDEO = "video"
    COMMENT = "comment"
    SYSTEM = "system"


class Action(str, Enum):
    """操作类型枚举"""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    MANAGE = "manage"  # 管理权限，包含所有操作
    
    # 特殊操作
    PUBLISH = "publish"
    APPROVE = "approve"
    FOLLOW = "follow"


class Scope(str, Enum):
    """权限范围枚举"""
    OWN = "own"      # 仅自己的资源
    ALL = "all"      # 所有资源
    PUBLIC = "public"  # 公开资源


@dataclass
class Permission:
    """权限定义"""
    resource: ResourceType
    action: Action
    scope: Scope = Scope.OWN
    
    def __str__(self) -> str:
        return f"{self.resource}:{self.action}:{self.scope}"
    
    @classmethod
    def from_string(cls, permission_str: str) -> "Permission":
        """从字符串创建权限对象"""
        parts = permission_str.split(":")
        if len(parts) != 3:
            raise ValueError(f"Invalid permission format: {permission_str}")
        
        return cls(
            resource=ResourceType(parts[0]),
            action=Action(parts[1]),
            scope=Scope(parts[2])
        )


class Role(str, Enum):
    """用户角色枚举"""
    SUPER_ADMIN = "super_admin"    # 超级管理员
    ADMIN = "admin"                # 管理员
    MODERATOR = "moderator"        # 版主
    USER = "user"                  # 普通用户
    GUEST = "guest"                # 访客


# 预定义权限常量
class Permissions:
    """权限常量定义"""
    
    # 用户相关权限
    USER_READ_OWN = Permission(ResourceType.USER, Action.READ, Scope.OWN)
    USER_READ_ALL = Permission(ResourceType.USER, Action.READ, Scope.ALL)
    USER_UPDATE_OWN = Permission(ResourceType.USER, Action.UPDATE, Scope.OWN)
    USER_UPDATE_ALL = Permission(ResourceType.USER, Action.UPDATE, Scope.ALL)
    USER_DELETE_ALL = Permission(ResourceType.USER, Action.DELETE, Scope.ALL)
    USER_MANAGE = Permission(ResourceType.USER, Action.MANAGE, Scope.ALL)
    USER_FOLLOW = Permission(ResourceType.USER, Action.FOLLOW, Scope.ALL)
    
    # 文章相关权限
    ARTICLE_READ_PUBLIC = Permission(ResourceType.ARTICLE, Action.READ, Scope.PUBLIC)
    ARTICLE_READ_OWN = Permission(ResourceType.ARTICLE, Action.READ, Scope.OWN)
    ARTICLE_READ_ALL = Permission(ResourceType.ARTICLE, Action.READ, Scope.ALL)
    ARTICLE_CREATE = Permission(ResourceType.ARTICLE, Action.CREATE, Scope.OWN)
    ARTICLE_UPDATE_OWN = Permission(ResourceType.ARTICLE, Action.UPDATE, Scope.OWN)
    ARTICLE_UPDATE_ALL = Permission(ResourceType.ARTICLE, Action.UPDATE, Scope.ALL)
    ARTICLE_DELETE_OWN = Permission(ResourceType.ARTICLE, Action.DELETE, Scope.OWN)
    ARTICLE_DELETE_ALL = Permission(ResourceType.ARTICLE, Action.DELETE, Scope.ALL)
    ARTICLE_PUBLISH_OWN = Permission(ResourceType.ARTICLE, Action.PUBLISH, Scope.OWN)
    ARTICLE_PUBLISH_ALL = Permission(ResourceType.ARTICLE, Action.PUBLISH, Scope.ALL)
    ARTICLE_APPROVE = Permission(ResourceType.ARTICLE, Action.APPROVE, Scope.ALL)
    ARTICLE_MANAGE = Permission(ResourceType.ARTICLE, Action.MANAGE, Scope.ALL)
    
    # 视频相关权限
    VIDEO_READ_PUBLIC = Permission(ResourceType.VIDEO, Action.READ, Scope.PUBLIC)
    VIDEO_READ_OWN = Permission(ResourceType.VIDEO, Action.READ, Scope.OWN)
    VIDEO_READ_ALL = Permission(ResourceType.VIDEO, Action.READ, Scope.ALL)
    VIDEO_CREATE = Permission(ResourceType.VIDEO, Action.CREATE, Scope.OWN)
    VIDEO_UPDATE_OWN = Permission(ResourceType.VIDEO, Action.UPDATE, Scope.OWN)
    VIDEO_UPDATE_ALL = Permission(ResourceType.VIDEO, Action.UPDATE, Scope.ALL)
    VIDEO_DELETE_OWN = Permission(ResourceType.VIDEO, Action.DELETE, Scope.OWN)
    VIDEO_DELETE_ALL = Permission(ResourceType.VIDEO, Action.DELETE, Scope.ALL)
    VIDEO_PUBLISH_OWN = Permission(ResourceType.VIDEO, Action.PUBLISH, Scope.OWN)
    VIDEO_PUBLISH_ALL = Permission(ResourceType.VIDEO, Action.PUBLISH, Scope.ALL)
    VIDEO_APPROVE = Permission(ResourceType.VIDEO, Action.APPROVE, Scope.ALL)
    VIDEO_MANAGE = Permission(ResourceType.VIDEO, Action.MANAGE, Scope.ALL)
    
    # 评论相关权限
    COMMENT_READ_PUBLIC = Permission(ResourceType.COMMENT, Action.READ, Scope.PUBLIC)
    COMMENT_CREATE = Permission(ResourceType.COMMENT, Action.CREATE, Scope.OWN)
    COMMENT_UPDATE_OWN = Permission(ResourceType.COMMENT, Action.UPDATE, Scope.OWN)
    COMMENT_DELETE_OWN = Permission(ResourceType.COMMENT, Action.DELETE, Scope.OWN)
    COMMENT_DELETE_ALL = Permission(ResourceType.COMMENT, Action.DELETE, Scope.ALL)
    COMMENT_MANAGE = Permission(ResourceType.COMMENT, Action.MANAGE, Scope.ALL)
    
    # 系统相关权限
    SYSTEM_MANAGE = Permission(ResourceType.SYSTEM, Action.MANAGE, Scope.ALL)


# 角色权限映射
ROLE_PERMISSIONS: Dict[Role, Set[Permission]] = {
    # 超级管理员：拥有所有权限
    Role.SUPER_ADMIN: {
        # 用户权限
        Permissions.USER_READ_ALL,
        Permissions.USER_UPDATE_ALL,
        Permissions.USER_DELETE_ALL,
        Permissions.USER_MANAGE,
        Permissions.USER_FOLLOW,
        
        # 文章权限
        Permissions.ARTICLE_READ_ALL,
        Permissions.ARTICLE_CREATE,
        Permissions.ARTICLE_UPDATE_ALL,
        Permissions.ARTICLE_DELETE_ALL,
        Permissions.ARTICLE_PUBLISH_ALL,
        Permissions.ARTICLE_APPROVE,
        Permissions.ARTICLE_MANAGE,
        
        # 视频权限
        Permissions.VIDEO_READ_ALL,
        Permissions.VIDEO_CREATE,
        Permissions.VIDEO_UPDATE_ALL,
        Permissions.VIDEO_DELETE_ALL,
        Permissions.VIDEO_PUBLISH_ALL,
        Permissions.VIDEO_APPROVE,
        Permissions.VIDEO_MANAGE,
        
        # 评论权限
        Permissions.COMMENT_READ_PUBLIC,
        Permissions.COMMENT_CREATE,
        Permissions.COMMENT_UPDATE_OWN,
        Permissions.COMMENT_DELETE_ALL,
        Permissions.COMMENT_MANAGE,
        
        # 系统权限
        Permissions.SYSTEM_MANAGE,
    },
    
    # 管理员：内容管理权限
    Role.ADMIN: {
        # 用户权限
        Permissions.USER_READ_ALL,
        Permissions.USER_FOLLOW,
        
        # 文章权限
        Permissions.ARTICLE_READ_ALL,
        Permissions.ARTICLE_CREATE,
        Permissions.ARTICLE_UPDATE_ALL,
        Permissions.ARTICLE_DELETE_ALL,
        Permissions.ARTICLE_PUBLISH_ALL,
        Permissions.ARTICLE_APPROVE,
        
        # 视频权限
        Permissions.VIDEO_READ_ALL,
        Permissions.VIDEO_CREATE,
        Permissions.VIDEO_UPDATE_ALL,
        Permissions.VIDEO_DELETE_ALL,
        Permissions.VIDEO_PUBLISH_ALL,
        Permissions.VIDEO_APPROVE,
        
        # 评论权限
        Permissions.COMMENT_READ_PUBLIC,
        Permissions.COMMENT_CREATE,
        Permissions.COMMENT_UPDATE_OWN,
        Permissions.COMMENT_DELETE_ALL,
    },
    
    # 版主：内容审核权限
    Role.MODERATOR: {
        # 用户权限
        Permissions.USER_READ_ALL,
        Permissions.USER_FOLLOW,
        
        # 文章权限
        Permissions.ARTICLE_READ_ALL,
        Permissions.ARTICLE_CREATE,
        Permissions.ARTICLE_UPDATE_OWN,
        Permissions.ARTICLE_DELETE_OWN,
        Permissions.ARTICLE_PUBLISH_OWN,
        Permissions.ARTICLE_APPROVE,
        
        # 视频权限
        Permissions.VIDEO_READ_ALL,
        Permissions.VIDEO_CREATE,
        Permissions.VIDEO_UPDATE_OWN,
        Permissions.VIDEO_DELETE_OWN,
        Permissions.VIDEO_PUBLISH_OWN,
        Permissions.VIDEO_APPROVE,
        
        # 评论权限
        Permissions.COMMENT_READ_PUBLIC,
        Permissions.COMMENT_CREATE,
        Permissions.COMMENT_UPDATE_OWN,
        Permissions.COMMENT_DELETE_ALL,
    },
    
    # 普通用户：基础权限
    Role.USER: {
        # 用户权限
        Permissions.USER_READ_OWN,
        Permissions.USER_UPDATE_OWN,
        Permissions.USER_FOLLOW,
        
        # 文章权限
        Permissions.ARTICLE_READ_PUBLIC,
        Permissions.ARTICLE_READ_OWN,
        Permissions.ARTICLE_CREATE,
        Permissions.ARTICLE_UPDATE_OWN,
        Permissions.ARTICLE_DELETE_OWN,
        Permissions.ARTICLE_PUBLISH_OWN,
        
        # 视频权限
        Permissions.VIDEO_READ_PUBLIC,
        Permissions.VIDEO_READ_OWN,
        Permissions.VIDEO_CREATE,
        Permissions.VIDEO_UPDATE_OWN,
        Permissions.VIDEO_DELETE_OWN,
        Permissions.VIDEO_PUBLISH_OWN,
        
        # 评论权限
        Permissions.COMMENT_READ_PUBLIC,
        Permissions.COMMENT_CREATE,
        Permissions.COMMENT_UPDATE_OWN,
        Permissions.COMMENT_DELETE_OWN,
    },
    
    # 访客：只读权限
    Role.GUEST: {
        Permissions.ARTICLE_READ_PUBLIC,
        Permissions.VIDEO_READ_PUBLIC,
        Permissions.COMMENT_READ_PUBLIC,
    },
}


class PermissionChecker:
    """权限检查器"""
    
    @staticmethod
    def get_user_role(user) -> Role:
        """获取用户角色"""
        if not user:
            return Role.GUEST
        
        if user.is_superuser:
            return Role.SUPER_ADMIN
        
        if user.role:
            role_name = user.role.name
            if role_name == "超级管理员":
                return Role.SUPER_ADMIN
            elif role_name == "管理员":
                return Role.ADMIN
            elif role_name == "版主":
                return Role.MODERATOR
            elif role_name == "普通用户":
                return Role.USER
        
        return Role.USER
    
    @staticmethod
    def get_user_permissions(user) -> Set[Permission]:
        """获取用户所有权限"""
        role = PermissionChecker.get_user_role(user)
        return ROLE_PERMISSIONS.get(role, set())
    
    @staticmethod
    def has_permission(user, permission: Permission) -> bool:
        """检查用户是否拥有指定权限"""
        user_permissions = PermissionChecker.get_user_permissions(user)
        
        # 检查是否有完全匹配的权限
        if permission in user_permissions:
            return True
        
        # 检查是否有管理权限（管理权限包含所有操作）
        manage_permission = Permission(permission.resource, Action.MANAGE, Scope.ALL)
        if manage_permission in user_permissions:
            return True
        
        # 检查是否有更高级别的权限
        if permission.scope == Scope.OWN:
            # 如果需要的是 OWN 权限，检查是否有 ALL 权限
            all_permission = Permission(permission.resource, permission.action, Scope.ALL)
            if all_permission in user_permissions:
                return True
        
        return False
    
    @staticmethod
    def check_resource_access(user, resource_type: ResourceType, action: Action, 
                            resource_owner_id: Optional[int] = None, 
                            is_public: bool = False) -> bool:
        """检查资源访问权限"""
        user_permissions = PermissionChecker.get_user_permissions(user)
        
        # 如果是公开资源且是读取操作
        if is_public and action == Action.READ:
            public_permission = Permission(resource_type, Action.READ, Scope.PUBLIC)
            if public_permission in user_permissions:
                return True
        
        # 检查是否是资源所有者
        if user and resource_owner_id and user.id == resource_owner_id:
            own_permission = Permission(resource_type, action, Scope.OWN)
            if PermissionChecker.has_permission(user, own_permission):
                return True
        
        # 检查是否有全局权限
        all_permission = Permission(resource_type, action, Scope.ALL)
        if PermissionChecker.has_permission(user, all_permission):
            return True
        
        return False
    
    @staticmethod
    def require_permission(user, permission: Permission) -> None:
        """要求用户拥有指定权限，否则抛出异常"""
        if not PermissionChecker.has_permission(user, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要 {permission} 权限"
            )
    
    @staticmethod
    def require_resource_access(user, resource_type: ResourceType, action: Action,
                              resource_owner_id: Optional[int] = None,
                              is_public: bool = False) -> None:
        """要求用户拥有资源访问权限，否则抛出异常"""
        if not PermissionChecker.check_resource_access(
            user, resource_type, action, resource_owner_id, is_public
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，无法对 {resource_type} 执行 {action} 操作"
            )
